'use strict';

const withdrawalService = ({ strapi }) => ({
  async listWithdrawals(query) {
    const {
      page = 1,
      pageSize = 10,
      search,
      status,
      startDate,
      endDate,
    } = query;

    // Build filters
    const filters = {};

    if (status) {
      filters.statusPaid = { $eq: status };
    }

    if (search) {
      filters.$or = [
        { user: { name: { $containsi: search } } },
        { user: { phone: { $containsi: search } } },
        { user: { email: { $containsi: search } } },
        { bankAccount: { $containsi: search } },
        { bankName: { $containsi: search } },
        { accountHolder: { $containsi: search } },
      ];
    }

    if (startDate || endDate) {
      filters.createdAt = {};
      if (startDate) {
        filters.createdAt.$gte = new Date(startDate);
      }
      if (endDate) {
        filters.createdAt.$lte = new Date(endDate + 'T23:59:59.999Z');
      }
    }

    try {
      const { results, pagination } = await strapi.entityService.findPage(
        'api::rut-tien.rut-tien',
        {
          filters,
          populate: {
            user: {
              populate: {
                image: true,
              },
            },
            processedBy: true,
          },
          sort: { createdAt: 'desc' },
          page: parseInt(page),
          pageSize: parseInt(pageSize),
        }
      );

      // Transform data to match frontend expectations
      const transformedData = results.map((withdrawal) => ({
        id: withdrawal.id,
        amount: parseFloat(withdrawal.amount),
        bankName: withdrawal.bankName,
        bankAccount: withdrawal.bankAccount,
        accountHolder: withdrawal.accountHolder,
        statusPaid: withdrawal.statusPaid,
        note: withdrawal.note,
        adminNote: withdrawal.adminNote,
        user: {
          id: withdrawal.user.id,
          name: withdrawal.user.name,
          phone: withdrawal.user.phone,
          email: withdrawal.user.email,
          avatar: withdrawal.user.image?.url,
          balance: parseFloat(withdrawal.user.balance || 0),
        },
        createdAt: withdrawal.createdAt,
        updatedAt: withdrawal.updatedAt,
        processedAt: withdrawal.processedAt,
        processedBy: withdrawal.processedBy
          ? {
              id: withdrawal.processedBy.id,
              name: `${withdrawal.processedBy.firstname} ${withdrawal.processedBy.lastname}`.trim(),
            }
          : null,
      }));

      return {
        data: transformedData,
        meta: { pagination },
      };
    } catch (error) {
      console.error('Error fetching withdrawals:', error);
      throw error;
    }
  },

  async getWithdrawalDetail(id) {
    try {
      const withdrawal = await strapi.entityService.findOne(
        'api::rut-tien.rut-tien',
        id,
        {
          populate: {
            user: {
              populate: {
                image: true,
              },
            },
            processedBy: true,
          },
        }
      );

      if (!withdrawal) {
        throw new Error('Withdrawal not found');
      }

      return {
        id: withdrawal.id,
        amount: parseFloat(withdrawal.amount),
        bankName: withdrawal.bankName,
        bankAccount: withdrawal.bankAccount,
        accountHolder: withdrawal.accountHolder,
        statusPaid: withdrawal.statusPaid,
        note: withdrawal.note,
        adminNote: withdrawal.adminNote,
        user: {
          id: withdrawal.user.id,
          name: withdrawal.user.name,
          phone: withdrawal.user.phone,
          email: withdrawal.user.email,
          avatar: withdrawal.user.image?.url,
          balance: parseFloat(withdrawal.user.balance || 0),
        },
        createdAt: withdrawal.createdAt,
        updatedAt: withdrawal.updatedAt,
        processedAt: withdrawal.processedAt,
        processedBy: withdrawal.processedBy
          ? {
              id: withdrawal.processedBy.id,
              name: `${withdrawal.processedBy.firstname} ${withdrawal.processedBy.lastname}`.trim(),
            }
          : null,
      };
    } catch (error) {
      console.error('Error fetching withdrawal detail:', error);
      throw error;
    }
  },

  async approveWithdrawal(id, adminUser) {
    try {
      const withdrawal = await strapi.entityService.findOne(
        'api::rut-tien.rut-tien',
        id,
        {
          populate: { user: true },
        }
      );

      if (!withdrawal) {
        throw new Error('Withdrawal not found');
      }

      if (withdrawal.statusPaid !== 'pending') {
        throw new Error('Withdrawal is not pending');
      }

      // Update withdrawal status
      const updatedWithdrawal = await strapi.entityService.update(
        'api::rut-tien.rut-tien',
        id,
        {
          data: {
            statusPaid: 'approved',
            processedAt: new Date(),
            processedBy: adminUser.id,
          } as any,
        }
      );

      return updatedWithdrawal;
    } catch (error) {
      console.error('Error approving withdrawal:', error);
      throw error;
    }
  },

  async rejectWithdrawal(id, adminNote, adminUser) {
    try {
      const withdrawal = await strapi.entityService.findOne(
        'api::rut-tien.rut-tien',
        id,
        {
          populate: { user: true },
        }
      );

      if (!withdrawal) {
        throw new Error('Withdrawal not found');
      }

      if (withdrawal.statusPaid !== 'pending') {
        throw new Error('Withdrawal is not pending');
      }

      // Update withdrawal status and refund balance
      const updatedWithdrawal = await strapi.entityService.update(
        'api::rut-tien.rut-tien',
        id,
        {
          data: {
            statusPaid: 'rejected',
            adminNote: adminNote,
            processedAt: new Date(),
            processedBy: adminUser.id,
          } as any,
        }
      );

      // Refund the amount to user balance
      const currentBalance = parseFloat(withdrawal.user.balance || 0);
      const refundAmount = parseFloat(withdrawal.amount);

      await strapi.entityService.update(
        'plugin::users-permissions.user',
        withdrawal.user.id,
        {
          data: {
            balance: currentBalance + refundAmount,
          } as any,
        }
      );

      return updatedWithdrawal;
    } catch (error) {
      console.error('Error rejecting withdrawal:', error);
      throw error;
    }
  },

  async getWithdrawalStatistics(query) {
    try {
      const { startDate, endDate } = query;

      // Build date filter
      const dateFilter = {};
      if (startDate || endDate) {
        if (startDate) {
          dateFilter.$gte = new Date(startDate);
        }
        if (endDate) {
          dateFilter.$lte = new Date(endDate + 'T23:59:59.999Z');
        }
      }

      // Get all withdrawals with date filter - without populate to avoid foreign key issues
      const allWithdrawals = await strapi.entityService.findMany(
        'api::rut-tien.rut-tien',
        {
          filters: Object.keys(dateFilter).length
            ? { createdAt: dateFilter }
            : {},
          // Don't populate relations to avoid foreign key errors
        }
      );

      // Calculate statistics
      const stats = allWithdrawals.reduce(
        (acc, withdrawal) => {
          const amount = parseFloat(withdrawal.amount);

          acc.totalWithdrawals++;
          acc.totalAmount += amount;

          switch (withdrawal.statusPaid) {
            case 'pending':
              acc.pendingWithdrawals++;
              acc.pendingAmount += amount;
              break;
            case 'approved':
              acc.approvedWithdrawals++;
              acc.approvedAmount += amount;
              break;
            case 'rejected':
              acc.rejectedWithdrawals++;
              acc.rejectedAmount += amount;
              break;
          }

          return acc;
        },
        {
          totalWithdrawals: 0,
          pendingWithdrawals: 0,
          approvedWithdrawals: 0,
          rejectedWithdrawals: 0,
          totalAmount: 0,
          pendingAmount: 0,
          approvedAmount: 0,
          rejectedAmount: 0,
        }
      );

      return stats;
    } catch (error) {
      console.error('Error fetching withdrawal statistics:', error);
      throw error;
    }
  },
});

module.exports = withdrawalService;

'use strict';

const orderService = ({ strapi }) => ({
  async createOrder(orderData) {
    try {
      const { customer, products, taxPercentage = 0, userId } = orderData;

      // Validate required fields
      if (!customer || !customer.name || !customer.phone) {
        throw new Error('Thông tin khách hàng không đầy đủ');
      }

      if (!products || products.length === 0) {
        throw new Error('Phải có ít nhất một sản phẩm');
      }

      // Calculate totals
      const subtotal = products.reduce((sum, product) => {
        return sum + product.price * product.quantity;
      }, 0);

      const taxAmount = (subtotal * taxPercentage) / 100;
      const priceAfterTax = subtotal + taxAmount;

      // Generate order code
      const orderCount = await strapi.entityService.count(
        'api::don-hang.don-hang'
      );
      const orderCode = `DH${String(orderCount + 1).padStart(6, '0')}`;

      // Create order data
      const newOrderData = {
        code: orderCode,
        priceAfterTax: priceAfterTax,
        taxAmount: taxAmount,
        discountAmount: 0,
        shippingAmount: 0,
        productSnapshot: products,
        customerSnapshot: customer,
        statusOrder: 'Chờ xác nhận',
        paymentStatus: false,
        user: userId || null,
      };

      // Create the order
      const createdOrder = await strapi.entityService.create(
        'api::don-hang.don-hang',
        {
          data: newOrderData,
          populate: {
            user: {
              fields: ['id', 'username', 'email'],
            },
          },
        }
      );

      return {
        data: {
          id: (createdOrder as any).id,
          code: (createdOrder as any).code,
          statusOrder: (createdOrder as any).statusOrder,
          priceAfterTax: (createdOrder as any).priceAfterTax,
          priceBeforeTax: subtotal,
          taxAmount: (createdOrder as any).taxAmount,
          customer: (createdOrder as any).customerSnapshot,
          products: (createdOrder as any).productSnapshot,
          createdAt: (createdOrder as any).createdAt,
          updatedAt: (createdOrder as any).updatedAt,
        },
      };
    } catch (error) {
      console.error('Error in createOrder:', error);
      throw error;
    }
  },

  async listOrders(query) {
    try {
      const {
        page = 1,
        pageSize = 10,
        status,
        code,
        startDate,
        endDate,
      } = query;

      // Build filters
      const filters = {};

      if (status) {
        filters.statusOrder = { $eq: status };
      }

      if (code) {
        const sanitizedCode = code
          .toString()
          .trim()
          .replace(/[<>'"\\{}[\]|&]/g, '') // Remove potentially problematic characters
          .substring(0, 100); // Limit length

        if (sanitizedCode.length > 0) {
          filters.code = { $containsi: sanitizedCode };
        }
      }

      if (startDate || endDate) {
        filters.createdAt = {};
        if (startDate) {
          filters.createdAt.$gte = new Date(startDate);
        }
        if (endDate) {
          filters.createdAt.$lte = new Date(endDate);
        }
      }

      // Get orders with pagination
      const orders = await strapi.entityService.findMany(
        'api::don-hang.don-hang',
        {
          filters,
          populate: {
            user: {
              fields: ['id', 'username', 'email'],
            },
            hoa_hongs: {
              populate: {
                user: {
                  fields: ['id', 'username', 'email', 'name', 'phone'],
                },
              },
            },
          },
          start: (parseInt(page) - 1) * parseInt(pageSize),
          limit: parseInt(pageSize),
          sort: { createdAt: 'desc' },
        }
      );

      // Get total count for pagination
      const total = await strapi.entityService.count('api::don-hang.don-hang', {
        filters,
      });

      // Transform data to match frontend expectations
      const transformedOrders = (Array.isArray(orders) ? orders : []).map(
        (order) => ({
          id: order.id,
          code: order.code,
          statusOrder: order.statusOrder,
          priceAfterTax: order.priceAfterTax,
          priceBeforeTax: order.priceAfterTax - order.taxAmount,
          taxAmount: order.taxAmount,
          customer: order.customerSnapshot,
          products: order.productSnapshot,
          hoa_hongs: order.hoa_hongs,
          createdAt: order.createdAt,
          updatedAt: order.updatedAt,
        })
      );

      const pageCount = Math.ceil(total / parseInt(pageSize));

      return {
        data: transformedOrders,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        pageCount,
      };
    } catch (error) {
      console.error('Error in listOrders:', error);
      throw error;
    }
  },

  async getOrderDetail(id) {
    try {
      const order = await strapi.entityService.findOne(
        'api::don-hang.don-hang',
        id,
        {
          populate: {
            user: {
              fields: ['id', 'username', 'email'],
            },
            hoa_hongs: {
              populate: {
                user: {
                  fields: ['id', 'username', 'email', 'name', 'phone'],
                },
              },
            },
          },
        }
      );

      if (!order) {
        throw new Error('Order not found');
      }

      const orderData = order as any;
      return {
        id: orderData.id,
        code: orderData.code,
        statusOrder: orderData.statusOrder,
        priceAfterTax: orderData.priceAfterTax,
        priceBeforeTax: orderData.priceAfterTax - orderData.taxAmount,
        taxAmount: orderData.taxAmount,
        customer: orderData.customerSnapshot,
        products: orderData.productSnapshot,
        user: orderData.user,
        hoa_hongs: orderData.hoa_hongs,
        createdAt: orderData.createdAt,
        updatedAt: orderData.updatedAt,
      };
    } catch (error) {
      console.error('Error in getOrderDetail:', error);
      throw error;
    }
  },

  async updateOrderStatus(id, status) {
    try {
      const updatedOrder = await strapi.entityService.update(
        'api::don-hang.don-hang',
        id,
        {
          data: {
            statusOrder: status,
          } as any,
        }
      );

      return {
        id: (updatedOrder as any).id,
        statusOrder: (updatedOrder as any).statusOrder,
      };
    } catch (error) {
      console.error('Error in updateOrderStatus:', error);
      throw error;
    }
  },

  async updateOrderCustomer(id, customer) {
    try {
      const updatedOrder = await strapi.entityService.update(
        'api::don-hang.don-hang',
        id,
        {
          data: {
            customerSnapshot: customer,
          } as any,
        }
      );

      return {
        id: (updatedOrder as any).id,
        customer: (updatedOrder as any).customerSnapshot,
      };
    } catch (error) {
      console.error('Error in updateOrderCustomer:', error);
      throw error;
    }
  },

  async exportOrdersToExcel(query) {
    try {
      // Get all orders based on query filters
      const ordersData = await this.listOrders({
        ...query,
        page: 1,
        pageSize: 10000, // Get all orders
      });

      // For now, return empty buffer - Excel generation will be handled on frontend
      return Buffer.from('');
    } catch (error) {
      console.error('Error in exportOrdersToExcel:', error);
      throw error;
    }
  },

  async updateCommission(id, commissionData) {
    try {
      // This would update commission data related to the order
      // Implementation depends on hoa-hong structure
      return { id, commissionData };
    } catch (error) {
      console.error('Error in updateCommission:', error);
      throw error;
    }
  },

  async getOrderStatistics(query) {
    try {
      // Get basic statistics
      const totalOrders = await strapi.entityService.count(
        'api::don-hang.don-hang'
      );

      const completedOrders = await strapi.entityService.findMany(
        'api::don-hang.don-hang',
        {
          filters: {
            statusOrder: { $eq: 'Đã hoàn thành' },
          },
          fields: ['priceAfterTax'],
        }
      );

      const ordersArray = Array.isArray(completedOrders) ? completedOrders : [];
      const totalRevenue = ordersArray.reduce((sum, order) => {
        return sum + (parseFloat(order.priceAfterTax) || 0);
      }, 0);

      return {
        totalOrders,
        totalRevenue,
      };
    } catch (error) {
      console.error('Error in getOrderStatistics:', error);
      throw error;
    }
  },
});

module.exports = orderService;
